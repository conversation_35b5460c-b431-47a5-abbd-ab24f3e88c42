"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function BrandsPage() {
  const router = useRouter();

  // Redirect to root to avoid duplicate content
  useEffect(() => {
    router.replace("/");
  }, [router]);

  // Show loading while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Redirecting...</p>
      </div>
    </div>
  );
}
