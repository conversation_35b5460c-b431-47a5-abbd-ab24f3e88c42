"""
Development environment configuration
"""
import os
from typing import Dict, Any
from .base import BaseConfig


class DevelopmentConfig(BaseConfig):
    """Configuration for development environment"""

    @property
    def firebase_config(self) -> Dict[str, Any]:
        """Firebase configuration for development"""
        return {
            "project_id": "admesh-dev",
            "api_key": "AIzaSyDxBNmjuoMjkS5u8iad6PSB_5Lm7ggIkfY",
            "auth_domain": "admesh-dev.firebaseapp.com",
            "storage_bucket": "admesh-dev.firebasestorage.app",
            "messaging_sender_id": "651813374456",
            "app_id": "1:651813374456:web:dfc618425534b042576e0d"
        }

    @property
    def api_base_url(self) -> str:
        """API base URL for development"""
        return os.getenv("API_BASE_URL", "http://localhost:8000")

    @property
    def frontend_url(self) -> str:
        """Frontend URL for development"""
        return os.getenv("FRONTEND_URL", "http://localhost:3000")

    @property
    def cors_origins(self) -> list:
        """CORS origins for development"""
        return [
            "http://localhost:3000",
            "http://localhost:3001",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
            "https://localhost:3000",
            "https://localhost:3001",
            "http://localhost:8000",  # Backend self-reference
            "http://127.0.0.1:8000",  # Backend self-reference
            "*"  # Allow all origins in development
        ]

    @property
    def database_config(self) -> Dict[str, Any]:
        """Database configuration for development"""
        return {
            "project_id": "admesh-dev",
            "database_id": "(default)",
            "emulator_host": os.getenv("FIRESTORE_EMULATOR_HOST"),
            "use_emulator": os.getenv("USE_FIRESTORE_EMULATOR", "false").lower() == "true"
        }

    @property
    def debug(self) -> bool:
        """Debug mode is enabled in development"""
        return True

    @property
    def log_level(self) -> str:
        """Logging level for development"""
        return os.getenv("LOG_LEVEL", "DEBUG").upper()

    def get_required_env_vars(self) -> list:
        """Required environment variables for development"""
        return [
            # Firebase credentials are optional in development if using default paths
            # No required environment variables for development
        ]

    @property
    def external_services(self) -> Dict[str, Any]:
        """External service configurations for development"""
        return {
            "openai": {
                "api_key": os.getenv("OPENAI_API_KEY"),
                "base_url": os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
                "model": os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")
            },
            "stripe": {
                "api_key": os.getenv("STRIPE_SECRET_KEY"),
                "publishable_key": os.getenv("STRIPE_PUBLISHABLE_KEY"),
                "webhook_secret": os.getenv("STRIPE_WEBHOOK_SECRET"),
                "use_test_mode": True
            },
            "resend": {
                "api_key": os.getenv("RESEND_API_KEY"),
                "from_email": os.getenv("RESEND_FROM_EMAIL", "<EMAIL>")
            }
        }

    @property
    def feature_flags(self) -> Dict[str, bool]:
        """Feature flags for development"""
        return {
            "enable_analytics": os.getenv("ENABLE_ANALYTICS", "false").lower() == "true",
            "enable_rate_limiting": os.getenv("ENABLE_RATE_LIMITING", "false").lower() == "true",
            "enable_caching": os.getenv("ENABLE_CACHING", "false").lower() == "true",
            "enable_email_verification": os.getenv("ENABLE_EMAIL_VERIFICATION", "false").lower() == "true",
            "enable_trust_score_throttling": os.getenv("ENABLE_TRUST_SCORE_THROTTLING", "false").lower() == "true"
        }
